#%% 对图像应用一致性聚类保存聚类的整体habitat-mask
import os
import re
import nibabel as nib
import numpy as np
from sklearn.cluster import KMeans
import warnings
import pickle
from scipy.ndimage import zoom

os.environ["OMP_NUM_THREADS"] = "1"
warnings.filterwarnings("ignore", category=UserWarning, module='sklearn.cluster._kmeans')

# 输入文件夹路径和文件名
image_folders = [r"N:\肝脏MRI数据集\HCC-TACE联合靶免\378靶免HCC最终版\378HCC\image\T1"]
mask_folder = r"N:\肝脏MRI数据集\HCC-TACE联合靶免\378靶免HCC最终版\378HCC\mask\T1"
output_folder = r"N:\肝脏MRI数据集\HCC-TACE联合靶免\378靶免HCC最终版\378HCC\habitatmask\T1"    

# 如果输出文件夹不存在，则创建
if not os.path.exists(output_folder):
    os.makedirs(output_folder)

failed_records = []

def _is_valid_affine(aff):
    try:
        if aff is None:
            return False
        a = np.asarray(aff)
        if a.shape != (4, 4):
            return False
        # 上三行全为0则无效
        if np.allclose(a[:3, :4], 0):
            return False
        # 旋转缩放子矩阵可逆
        _ = np.linalg.det(a[:3, :3])
        if np.isclose(_, 0):
            return False
        return True
    except Exception:
        return False

def _get_safe_affine(nib_imgs):
    try:
        aff = nib_imgs[0].affine if nib_imgs else None
    except Exception:
        aff = None
    if not _is_valid_affine(aff):
        print("[警告] 源图像仿射矩阵无效，使用单位仿射矩阵保存输出。")
        return np.eye(4)
    return aff

def resample_mask_to_image(mask_data, target_shape):
    """
    使用scipy.ndimage.zoom重采样mask到目标形状
    """
    if mask_data.shape == target_shape:
        return mask_data

    # 确保维度匹配
    if len(mask_data.shape) != len(target_shape):
        return mask_data

    # 计算缩放因子
    zoom_factors = [target_shape[i] / mask_data.shape[i] for i in range(len(target_shape))]

    try:
        # 使用最近邻插值重采样mask
        resampled_mask = zoom(mask_data, zoom_factors, order=0)
        return resampled_mask
    except Exception as e:
        return mask_data

def collect_all_data(image_folders, mask_folder, sample_ratio=0.1):
    """
    收集所有图像的数据用于训练全局聚类模型
    sample_ratio: 采样比例，避免内存不足
    """
    all_data = []
    processed_count = 0
    skipped_count = 0

    mask_files = [f for f in os.listdir(mask_folder) if f.endswith(".nii.gz")]
    total_files = len(mask_files)
    print(f"开始收集数据，共 {total_files} 个mask文件...")

    for idx, filename in enumerate(mask_files, 1):
        if filename.endswith(".nii.gz"):
            try:
                # 加载mask文件
                mask_path = os.path.join(mask_folder, filename)
                mask = nib.load(mask_path).get_fdata()

                # 检查mask是否为空
                if not np.any(mask > 0):
                    print(f"跳过空mask文件: {filename}")
                    skipped_count += 1
                    continue

                # 提取第一个 - 前面的名字作为匹配基准
                mask_prefix = filename.split('-')[0] if '-' in filename else filename.split('.')[0]

                # 检查对应的图像文件是否存在
                imgs = []
                for image_folder in image_folders:
                    found_img = False
                    # 遍历图像文件夹中的所有文件
                    for img_file in os.listdir(image_folder):
                        if img_file.endswith(('.nii', '.nii.gz')):
                            # 提取图像文件第一个 - 前面的名字
                            img_prefix = img_file.split('-')[0] if '-' in img_file else img_file.split('.')[0]
                            # 如果前缀匹配，则配对成功
                            if img_prefix == mask_prefix:
                                img_path = os.path.join(image_folder, img_file)
                                imgs.append(nib.load(img_path))
                                found_img = True
                                break

                    if not found_img:
                        print(f"未找到对应图像文件: {filename} (前缀={mask_prefix})")
                        break

                if len(imgs) != len(image_folders):
                    skipped_count += 1
                    continue

                img_data = [img.get_fdata() for img in imgs]

                # 检查img_data是否为空
                if not img_data:
                    print(f"图像数据为空: {filename}")
                    skipped_count += 1
                    continue

                # 调整mask大小以匹配图像
                original_mask_shape = mask.shape
                for i, img in enumerate(img_data):
                    # 只比较前3个维度（空间维度），忽略通道维度
                    img_spatial_shape = img.shape[:3] if len(img.shape) > 3 else img.shape
                    if mask.shape != img_spatial_shape:
                        print(f"调整mask大小: {filename} - 从 {mask.shape} 到 {img_spatial_shape}")
                        mask = resample_mask_to_image(mask, img_spatial_shape)
                        break

                # 提取ROI内的数据
                if np.any(mask > 0):
                    roi_data = []
                    for img in img_data:
                        roi_pixels = img[mask > 0]
                        if len(roi_pixels) == 0:
                            print(f"ROI区域为空: {filename}")
                            break
                        roi_data.append(roi_pixels)

                    # 检查ROI数据是否有效
                    if len(roi_data) == len(img_data) and all(len(data) > 0 for data in roi_data):
                        # 确保每个roi_data是1D数组
                        roi_data_clean = []
                        for data in roi_data:
                            if data.ndim > 1:
                                data = data.flatten()
                            roi_data_clean.append(data)

                        # 检查所有ROI数据长度是否一致
                        lengths = [len(data) for data in roi_data_clean]
                        if len(set(lengths)) > 1:
                            print(f"ROI数据长度不一致: {filename} - 长度: {lengths}")
                            skipped_count += 1
                            continue

                        # 使用column_stack而不是stack来避免维度问题
                        try:
                            if len(roi_data_clean) == 1:
                                X = roi_data_clean[0].reshape(-1, 1)
                            else:
                                X = np.column_stack(roi_data_clean)

                            # 检查是否有无效值
                            if np.any(np.isnan(X)) or np.any(np.isinf(X)):
                                print(f"数据包含无效值: {filename}")
                                skipped_count += 1
                                continue

                            # 归一化处理
                            if X.max() != X.min():
                                X = (X - X.min()) / (X.max() - X.min())
                            else:
                                print(f"数据无变化，跳过: {filename}")
                                skipped_count += 1
                                continue

                            # 随机采样以减少内存使用
                            if len(X) > 0:
                                n_samples = max(1, int(len(X) * sample_ratio))
                                indices = np.random.choice(len(X), n_samples, replace=False)
                                all_data.append(X[indices])
                                processed_count += 1
                                # 每10个文件显示一次进度
                                if idx % 10 == 0 or idx == total_files:
                                    print(f"进度: {idx}/{total_files} - 已处理: {processed_count}, 跳过: {skipped_count}")

                        except Exception as e:
                            print(f"数据处理错误: {filename} - {e}")
                            failed_records.append((filename, f"数据处理错误: {e}"))
                            skipped_count += 1
                            continue
                    else:
                        print(f"ROI数据无效: {filename}")
                        skipped_count += 1
                        continue
                else:
                    print(f"mask中没有有效区域: {filename}")
                    skipped_count += 1
                    continue

            except Exception as e:
                failed_records.append((filename, f"收集数据错误: {e}"))
                skipped_count += 1
                continue

    print(f"数据收集完成: 成功处理 {processed_count} 个文件, 跳过 {skipped_count} 个文件")

    if all_data:
        try:
            result = np.vstack(all_data)
            print(f"最终数据形状: {result.shape}")
            return result
        except Exception as e:
            print(f"合并数据时出错: {e}")
            return None
    else:
        print("没有收集到有效数据")
        return None

def train_global_kmeans(all_data, n_clusters):
    """训练全局KMeans模型"""
    kmeans = KMeans(n_clusters=n_clusters, random_state=0, n_init=10)
    kmeans.fit(all_data)
    return kmeans

def sort_clusters_by_intensity(kmeans_model, all_data, n_clusters):
    """
    根据聚类中心的平均强度对聚类进行排序
    特殊映射：标签3=最低密度（蓝色），其他按密度从高到低排列
    """
    centers = kmeans_model.cluster_centers_
    # 计算每个聚类中心的平均强度（假设第一个通道是主要强度信息）
    center_intensities = centers[:, 0] if centers.shape[1] > 0 else centers.mean(axis=1)

    # 获取按强度从高到低排序的索引（降序排列）
    sorted_indices = np.argsort(center_intensities)[::-1]  # [::-1]实现降序

    # 创建特殊标签映射：确保标签3是最低密度
    if n_clusters >= 3:
        # 标签3固定为最低密度
        label_mapping = {}
        # 最低密度 -> 标签3
        label_mapping[sorted_indices[-1]] = 2  # 标签3 (索引2)

        # 其余按密度从高到低分配到剩余标签
        remaining_labels = list(range(n_clusters))
        remaining_labels.remove(2)  # 移除已分配的标签3

        remaining_indices = sorted_indices[:-1]  # 除了最低密度的其他聚类
        for i, old_idx in enumerate(remaining_indices):
            if i < len(remaining_labels):
                label_mapping[old_idx] = remaining_labels[i]
    else:
        # 如果聚类数<3，使用原来的映射方式
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted_indices)}


    return label_mapping

def apply_consistent_clustering(imgs, mask, kmeans_model, label_mapping):
    """使用全局模型进行一致性聚类"""
    # 输出结果与 ROI 的空间维度应一致，避免通道维导致维度不匹配
    clustered_img = np.zeros(mask.shape)

    if not np.any(mask > 0):
        print("警告: mask中没有有效区域")
        return clustered_img

    try:
        # 提取ROI数据
        roi_voxels = np.sum(mask > 0)  # 计算mask中的体素数量
        print(f"ROI体素数量: {roi_voxels}")

        # 正确提取每个图像在mask区域的数据
        roi_data_list = []
        for i, img in enumerate(imgs):
            roi_data = img[mask > 0]  # 提取mask区域的数据

            # 检查提取的数据是否有效
            if len(roi_data) == 0:
                print(f"警告: 图像 {i} 在ROI区域没有数据")
                return clustered_img

            # 确保roi_data是1D数组
            if roi_data.ndim > 1:
                roi_data = roi_data.flatten()
            roi_data_list.append(roi_data)

        # 检查所有ROI数据长度是否一致
        lengths = [len(data) for data in roi_data_list]
        if len(set(lengths)) > 1:
            print(f"警告: ROI数据长度不一致: {lengths}")
            return clustered_img

        # 将所有图像的ROI数据堆叠成特征矩阵 (n_voxels, n_images)
        try:
            if len(roi_data_list) == 1:
                # 只有一个图像时，reshape为2D
                X = roi_data_list[0].reshape(-1, 1)
            else:
                # 多个图像时，使用column_stack
                X = np.column_stack(roi_data_list)
        except Exception as e:
            print(f"数据堆叠错误: {e}")
            return clustered_img

        # 确保X是2D数组
        if X.ndim > 2:
            X = X.reshape(X.shape[0], -1)

        # 检查是否有无效值
        if np.any(np.isnan(X)) or np.any(np.isinf(X)):
            print("警告: 数据包含无效值")
            return clustered_img

        # 归一化处理
        if X.max() != X.min():
            X = (X - X.min()) / (X.max() - X.min())
        else:
            print("警告: 数据无变化，无法归一化")
            return clustered_img

        # 使用全局模型预测
        try:
            labels = kmeans_model.predict(X)
        except Exception as e:
            print(f"聚类预测错误: {e}")
            return clustered_img

        # 应用标签映射确保一致性
        try:
            mapped_labels = np.array([label_mapping[label] for label in labels])
        except KeyError as e:
            print(f"标签映射错误: {e}")
            return clustered_img

        # 检查维度匹配
        if len(mapped_labels) != roi_voxels:
            print(f"维度不匹配: mapped_labels长度={len(mapped_labels)}, roi_voxels={roi_voxels}")
            return clustered_img

        # 将结果保存到3D图像中（+1是为了避免0标签）
        try:
            mapped_labels_plus_one = mapped_labels + 1
            if mapped_labels_plus_one.shape != (roi_voxels,):
                mapped_labels_plus_one = mapped_labels_plus_one.flatten()[:roi_voxels]
            clustered_img[mask > 0] = mapped_labels_plus_one
        except Exception as e:
            print(f"标签赋值错误，使用备用方法: {e}")
            try:
                mask_indices = np.where(mask > 0)
                for i, (z, y, x) in enumerate(zip(*mask_indices)):
                    if i < len(mapped_labels):
                        clustered_img[z, y, x] = mapped_labels[i] + 1
            except Exception as e2:
                print(f"备用方法也失败: {e2}")
                return clustered_img

    except Exception as e:
        print(f"聚类过程中出现未预期错误: {e}")
        return clustered_img

    return clustered_img

# 主要处理流程
n_clusters = 3

# 步骤1: 收集所有数据
all_data = collect_all_data(image_folders, mask_folder, sample_ratio=0.1)

if all_data is None:
    exit()

# 步骤2: 训练/加载全局KMeans模型
model_path = os.path.join(output_folder, 'global_kmeans_model.pkl')
if os.path.exists(model_path):
    with open(model_path, 'rb') as f:
        saved = pickle.load(f)
        global_kmeans = saved['model']
        label_mapping = saved.get('label_mapping')
else:
    global_kmeans = train_global_kmeans(all_data, n_clusters)
    # 步骤3: 根据强度排序聚类
    label_mapping = sort_clusters_by_intensity(global_kmeans, all_data, n_clusters)
    # 保存全局模型和标签映射
    with open(model_path, 'wb') as f:
        pickle.dump({'model': global_kmeans, 'label_mapping': label_mapping}, f)

# 步骤4: 对每个图像应用一致性聚类

# 统计总文件数
mask_files_list = [f for f in sorted(os.listdir(mask_folder)) if f.endswith(".nii.gz") or f.endswith(".nii")]
total_mask_files = len(mask_files_list)
processed_files = 0
skipped_files = 0

print(f"\n开始应用聚类，共 {total_mask_files} 个文件...")

for idx, filename in enumerate(mask_files_list, 1):
    # 改进：生成输出文件名，兼容有无"mask"的情况
    if "mask" in filename.lower():
        # 如果包含mask，替换为habitat
        output_filename = re.sub(r'-?mask', '-habitat', filename, flags=re.IGNORECASE)
    else:
        # 如果不包含mask，在扩展名前插入-habitat
        if filename.endswith(".nii.gz"):
            output_filename = filename.replace(".nii.gz", "-habitat.nii.gz")
        else:
            output_filename = filename.replace(".nii", "-habitat.nii")

    out_path = os.path.join(output_folder, output_filename)
    moved_path = os.path.join(output_folder, 'habitat', output_filename)

    # 显示处理进度
    print(f"\n[{idx}/{total_mask_files}] 正在处理: {filename}")

    if os.path.exists(out_path) or os.path.exists(moved_path):
        print(f"  -> 跳过(文件已存在)")
        skipped_files += 1
        continue

    try:
        # 加载mask文件
        mask_path = os.path.join(mask_folder, filename)
        mask = nib.load(mask_path).get_fdata()

        # 检查mask是否为空
        if not np.any(mask > 0):
            print(f"  -> 跳过(空mask文件)")
            skipped_files += 1
            continue

        # 提取第一个 - 前面的名字作为匹配基准
        mask_prefix = filename.split('-')[0] if '-' in filename else filename.split('.')[0]

        # 尝试加载图像文件
        imgs = []
        for image_folder in image_folders:
            found_img = False
            # 遍历图像文件夹中的所有文件
            for img_file in os.listdir(image_folder):
                if img_file.endswith(('.nii', '.nii.gz')):
                    # 提取图像文件第一个 - 前面的名字
                    img_prefix = img_file.split('-')[0] if '-' in img_file else img_file.split('.')[0]
                    # 如果前缀匹配，则配对成功
                    if img_prefix == mask_prefix:
                        img_path = os.path.join(image_folder, img_file)
                        imgs.append(nib.load(img_path))
                        found_img = True
                        break

            if not found_img:
                print(f"  -> 未找到对应图像文件 (前缀={mask_prefix})")
                break

        if len(imgs) != len(image_folders):
            print(f"  -> 跳过(缺少对应图像)")
            skipped_files += 1
            continue

        img_data = [img.get_fdata() for img in imgs]

        # 检查img_data是否为空
        if not img_data:
            print(f"  -> 跳过(图像数据为空)")
            skipped_files += 1
            continue

        # 调整mask大小以匹配图像
        for img in img_data:
            # 只比较前3个维度（空间维度），忽略通道维度
            img_spatial_shape = img.shape[:3] if len(img.shape) > 3 else img.shape
            if mask.shape != img_spatial_shape:
                print(f"调整mask大小: {filename} - 从 {mask.shape} 到 {img_spatial_shape}")
                mask = resample_mask_to_image(mask, img_spatial_shape)
                break

        # 再次检查调整后的mask是否有效
        if not np.any(mask > 0):
            print(f"  -> 跳过(调整后mask为空)")
            skipped_files += 1
            continue

        # 应用一致性聚类
        print(f"  -> 执行聚类...")
        habitat = apply_consistent_clustering(img_data, mask, global_kmeans, label_mapping)

        # 检查聚类结果是否有效
        if not np.any(habitat > 0):
            print(f"  -> 失败(聚类结果为空)")
            skipped_files += 1
            continue

        # 验证聚类结果并打印信息
        unique_labels = np.unique(habitat[habitat > 0])
        # 改进：提取患者ID
        patient_id = re.sub(r'-?(mask|habitat|cluster\d+)', '', filename, flags=re.IGNORECASE)
        patient_id = patient_id.replace('.nii.gz', '').replace('.nii', '')
        print(f"  -> 聚类标签: {unique_labels}")

        # 保存整体habitat结果
        print(f"  -> 保存habitat文件...")
        _aff = _get_safe_affine(imgs)
        habitat_img = nib.Nifti1Image(habitat, _aff)
        # 使用之前生成的output_filename
        nib.save(habitat_img, os.path.join(output_folder, output_filename))

        # 分别保存每个聚类的mask
        for cluster_id in range(1, n_clusters + 1):
            # 改进：生成cluster文件名，兼容有无"mask"的情况
            if "mask" in filename.lower():
                cluster_output_filename = re.sub(r'-?mask', f'-cluster{cluster_id}', filename, flags=re.IGNORECASE)
            else:
                if filename.endswith(".nii.gz"):
                    cluster_output_filename = filename.replace(".nii.gz", f"-cluster{cluster_id}.nii.gz")
                else:
                    cluster_output_filename = filename.replace(".nii", f"-cluster{cluster_id}.nii")

            cluster_out_path = os.path.join(output_folder, cluster_output_filename)
            cluster_moved_path = os.path.join(output_folder, f"cluster{cluster_id}.nii", cluster_output_filename)

            if os.path.exists(cluster_out_path) or os.path.exists(cluster_moved_path):
                continue

            # 创建cluster mask（只包含该聚类的体素）
            cluster_mask = np.zeros_like(habitat)
            cluster_mask[habitat == cluster_id] = 1

            # 保存cluster mask
            cluster_mask_img = nib.Nifti1Image(cluster_mask, _aff)
            nib.save(cluster_mask_img, cluster_out_path)

            # 打印每个cluster的体素数量
            voxel_count = np.sum(cluster_mask == 1)
            if voxel_count > 0:
                print(f"     Cluster {cluster_id}: {voxel_count} 体素")

        print(f"  -> 完成!")
        processed_files += 1

    except Exception as e:
        print(f"  -> 失败(错误: {str(e)[:50]}...)")
        failed_records.append((filename, f"处理阶段错误: {e}"))

print(f"\n聚类应用完成: 成功 {processed_files} 个, 跳过 {skipped_files} 个\n")

# 步骤5: 对每个图像生成分别的聚类mask文件（基于已保存的habitat结果）

habitat_folder = os.path.join(output_folder, 'habitat')
if os.path.exists(habitat_folder):
    # 从habitat文件生成cluster masks
    for habitat_file in os.listdir(habitat_folder):
        if (habitat_file.endswith(".nii.gz") or habitat_file.endswith(".nii")) and "habitat" in habitat_file.lower():
            # 改进：从habitat文件名还原原始文件名
            if "mask" in habitat_file.lower():
                original_filename = re.sub(r'-?habitat', '-mask', habitat_file, flags=re.IGNORECASE)
            else:
                # 如果原本没有mask，去掉-habitat
                original_filename = re.sub(r'-habitat', '', habitat_file, flags=re.IGNORECASE)

            expected_pairs = []
            for cid in range(1, n_clusters + 1):
                # 改进：生成cluster文件名
                if "mask" in original_filename.lower():
                    fn = re.sub(r'-?mask', f'-cluster{cid}', original_filename, flags=re.IGNORECASE)
                else:
                    if original_filename.endswith(".nii.gz"):
                        fn = original_filename.replace(".nii.gz", f"-cluster{cid}.nii.gz")
                    else:
                        fn = original_filename.replace(".nii", f"-cluster{cid}.nii")
                p_root = os.path.join(output_folder, fn)
                p_moved = os.path.join(output_folder, f"cluster{cid}.nii", fn)
                expected_pairs.append((p_root, p_moved))

            if all(os.path.exists(p_root) or os.path.exists(p_moved) for p_root, p_moved in expected_pairs):
                continue

            try:
                # 加载habitat结果
                habitat_path = os.path.join(habitat_folder, habitat_file)
                habitat = nib.load(habitat_path).get_fdata()

                # 分别保存每个聚类的mask
                for cluster_id in range(1, n_clusters + 1):
                    # 改进：生成cluster文件名
                    if "mask" in original_filename.lower():
                        output_filename = re.sub(r'-?mask', f'-cluster{cluster_id}', original_filename, flags=re.IGNORECASE)
                    else:
                        if original_filename.endswith(".nii.gz"):
                            output_filename = original_filename.replace(".nii.gz", f"-cluster{cluster_id}.nii.gz")
                        else:
                            output_filename = original_filename.replace(".nii", f"-cluster{cluster_id}.nii")
                    out_path = os.path.join(output_folder, output_filename)
                    moved_path = os.path.join(output_folder, f"cluster{cluster_id}.nii", output_filename)
                    
                    if os.path.exists(out_path) or os.path.exists(moved_path):
                        continue
                        
                    cluster_mask = np.zeros_like(habitat)
                    cluster_mask[habitat == cluster_id] = 1
                    
                    # 使用原habitat文件的仿射矩阵
                    cluster_mask_img = nib.Nifti1Image(cluster_mask, nib.load(habitat_path).affine)
                    nib.save(cluster_mask_img, out_path)
                    
                    
            except Exception as e:
                failed_records.append((habitat_file, f"cluster生成错误: {e}"))
else:
    pass

#%% 每一份聚类分割mask文件移动到指定的cluster文件夹，并将生境文件移动到habitat文件夹
# 为提取每一份聚类分割mask文件的影像组学特征做准备
import shutil

path = output_folder  # 同上面的路径

# 创建一个字典以存储具有相同后缀的文件
file_dict = {}

# 遍历目录中的所有文件
for file_name in os.listdir(path):
    if "cluster" in file_name:
        # 改进：提取cluster后的数字部分作为后缀
        # 例如：曹绪荣-PP-cluster1.nii.gz -> suffix = "1"
        match = re.search(r'cluster(\d+)', file_name)
        if match:
            suffix = match.group(1)  # 只提取数字
            # 将文件添加到字典中，使用相应的后缀作为键
            if suffix in file_dict:
                file_dict[suffix].append(file_name)
            else:
                file_dict[suffix] = [file_name]
        else:
            print(f"警告: 无法识别cluster编号: {file_name}")
    elif "habitat" in file_name:  # 检查文件名中是否包含"habitat"
        # 创建habitat目录
        habitat_dir = os.path.join(path, 'habitat')
        os.makedirs(habitat_dir, exist_ok=True)
        # 移动文件到habitat目录
        shutil.move(os.path.join(path, file_name), habitat_dir)

# 遍历字典并将文件复制到相应的目录
for suffix, file_list in file_dict.items():
    # 创建一个以后缀命名的目录
    dir_name = os.path.join(path, 'cluster' + suffix)
    # 创建目录，如果已存在则不报错
    os.makedirs(dir_name, exist_ok=True)
    # 将文件移动到该目录
    for file_name in file_list:
        shutil.move(os.path.join(path, file_name), dir_name)

print("文件移动完成！")

# 打印聚类结果总结
print("\n=== 聚类结果总结 ===")
print(f"聚类数量: {n_clusters}")
if n_clusters >= 3:
    print("标签含义:")
    print("  - 标签1: 高密度区域")
    print("  - 标签2: 中密度区域") 
    print("  - 标签3: 最低密度区域 (蓝色显示)")
else:
    print("标签含义:")
    print(f"  - 标签1: 最高密度区域")
    print(f"  - 标签{n_clusters}: 最低密度区域")

# 汇总所有处理失败的文件
print("\n" + "="*60)
print("处理结果汇总")
print("="*60)
if 'failed_records' in globals() and len(failed_records) > 0:
    print(f"\n处理失败的文件 ({len(failed_records)}个):")

    # 保存报错信息到文件
    error_log_path = os.path.join(output_folder, "报错文件名和原因.txt")
    with open(error_log_path, 'w', encoding='utf-8') as f:
        f.write("="*60 + "\n")
        f.write(f"处理失败的文件 ({len(failed_records)}个)\n")
        f.write("="*60 + "\n\n")

        for idx, (fname, err) in enumerate(failed_records, 1):
            error_msg = f"{idx}. 文件名: {fname}\n   原因: {err}\n\n"
            print(f"  - {fname}: {err}")
            f.write(error_msg)

        f.write("="*60 + "\n")

    print(f"\n报错信息已保存到: {error_log_path}")
else:
    print("\n所有文件处理成功！")
print("="*60)